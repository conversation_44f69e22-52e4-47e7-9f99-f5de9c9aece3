"use client";

import * as React from "react";
import useEmblaCarousel from "embla-carousel-react";
import { WheelGesturesPlugin } from "embla-carousel-wheel-gestures";
import type { EmblaOptionsType } from "embla-carousel";
import { cn } from "@/libs/utils";

// Native scrollbar with custom styling and interactive functionality

interface NativeScrollbarOverlayProps {
  trackColor?: string;
  thumbColor?: string;
  showProgressBar?: boolean;
  className?: string;
  onSeek?: (position: number) => void;
  containerRef: React.RefObject<HTMLDivElement | null>;
}

// Interactive overlay for native scrollbars
const NativeScrollbarOverlay: React.FC<NativeScrollbarOverlayProps> =
  React.memo(
    ({
      trackColor = "#FFF7E7",
      thumbColor = "#888",
      showProgressBar = true,
      className = "",
      onSeek,
      containerRef,
    }) => {
      const overlayRef = React.useRef<HTMLDivElement>(null);
      const [isDragging, setIsDragging] = React.useState(false);

      // Handle click on overlay (click-to-seek)
      const handleOverlayClick = React.useCallback(
        (event: React.MouseEvent) => {
          if (!onSeek || isDragging || !overlayRef.current) return;

          const rect = overlayRef.current.getBoundingClientRect();
          const relativeX = event.clientX - rect.left;
          const progress = relativeX / rect.width;
          onSeek(Math.max(0, Math.min(1, progress)));
        },
        [onSeek, isDragging]
      );

      // Handle mouse down (start drag)
      const handleMouseDown = React.useCallback(
        (event: React.MouseEvent) => {
          event.preventDefault();
          setIsDragging(true);
          handleOverlayClick(event);
        },
        [handleOverlayClick]
      );

      // Handle mouse move during drag
      const handleMouseMove = React.useCallback(
        (event: MouseEvent) => {
          if (!isDragging || !onSeek || !overlayRef.current) return;
          event.preventDefault();

          const rect = overlayRef.current.getBoundingClientRect();
          const relativeX = event.clientX - rect.left;
          const progress = relativeX / rect.width;
          onSeek(Math.max(0, Math.min(1, progress)));
        },
        [isDragging, onSeek]
      );

      // Handle mouse up (end drag)
      const handleMouseUp = React.useCallback(() => {
        setIsDragging(false);
      }, []);

      // Handle touch events
      const handleTouchStart = React.useCallback(
        (event: React.TouchEvent) => {
          event.preventDefault();
          setIsDragging(true);

          if (!onSeek || !overlayRef.current) return;
          const touch = event.touches[0];
          if (touch) {
            const rect = overlayRef.current.getBoundingClientRect();
            const relativeX = touch.clientX - rect.left;
            const progress = relativeX / rect.width;
            onSeek(Math.max(0, Math.min(1, progress)));
          }
        },
        [onSeek]
      );

      const handleTouchMove = React.useCallback(
        (event: TouchEvent) => {
          if (!isDragging || !onSeek || !overlayRef.current) return;
          event.preventDefault();

          const touch = event.touches[0];
          if (touch) {
            const rect = overlayRef.current.getBoundingClientRect();
            const relativeX = touch.clientX - rect.left;
            const progress = relativeX / rect.width;
            onSeek(Math.max(0, Math.min(1, progress)));
          }
        },
        [isDragging, onSeek]
      );

      const handleTouchEnd = React.useCallback(() => {
        setIsDragging(false);
      }, []);

      // Set up global event listeners
      React.useEffect(() => {
        if (isDragging) {
          document.addEventListener("mousemove", handleMouseMove);
          document.addEventListener("mouseup", handleMouseUp);
          document.addEventListener("touchmove", handleTouchMove);
          document.addEventListener("touchend", handleTouchEnd);

          return () => {
            document.removeEventListener("mousemove", handleMouseMove);
            document.removeEventListener("mouseup", handleMouseUp);
            document.removeEventListener("touchmove", handleTouchMove);
            document.removeEventListener("touchend", handleTouchEnd);
          };
        }
      }, [
        isDragging,
        handleMouseMove,
        handleMouseUp,
        handleTouchMove,
        handleTouchEnd,
      ]);

      if (!showProgressBar) return null;

      return (
        <div className={`w-full ${className}`}>
          {/* Invisible overlay for interaction */}
          <div
            ref={overlayRef}
            className="w-full h-1.5 absolute top-0 left-0 z-10 cursor-pointer"
            onMouseDown={handleMouseDown}
            onTouchStart={handleTouchStart}
            style={{
              cursor: isDragging ? "grabbing" : "pointer",
            }}
          />
        </div>
      );
    }
  );

NativeScrollbarOverlay.displayName = "NativeScrollbarOverlay";

interface UniversalCarouselProps {
  children: React.ReactNode;
  className?: string;
  itemClassName?: string;
  options?: EmblaOptionsType;
  useNativeScrollbar?: boolean;
  hideScrollbar?: boolean;
  gapClassName?: string;
  scrollbarColor?: string;
  scrollBarTrackColor?: string;
  showProgressBar?: boolean;
  progressBarTrackColor?: string;
  progressBarThumbColor?: string;
}

export function UniversalCarousel({
  children,
  className,
  itemClassName,
  options = {
    align: "start",
    skipSnaps: false,
    dragFree: true,
  },
  useNativeScrollbar = false,
  hideScrollbar = false,
  gapClassName = "gap-5",
  scrollbarColor,
  scrollBarTrackColor,
  showProgressBar = true,
  progressBarTrackColor = "#FFF7E7",
  progressBarThumbColor = "#888",
}: UniversalCarouselProps) {
  const containerRef = React.useRef<HTMLDivElement>(null);
  const [shouldScroll, setShouldScroll] = React.useState(true);
  const [scrollProgress, setScrollProgress] = React.useState(0);
  const [contentRatio, setContentRatio] = React.useState(0.3); // Ratio of visible to total content

  // Configure the wheel gestures plugin
  const wheelGesturesPlugin = WheelGesturesPlugin({
    forceWheelAxis: "x",
    wheelEnabled: true,
    wheelToScroll: true,
  });

  // Only include the wheel plugin if scrolling is needed and not using native scrollbar
  const plugins =
    shouldScroll && !useNativeScrollbar ? [wheelGesturesPlugin] : [];

  // Always call useEmblaCarousel hook unconditionally
  const [emblaRef, emblaApi] = useEmblaCarousel(
    {
      ...options,
      // Enable/disable dragging based on whether content needs scrolling
      watchDrag: shouldScroll,
    },
    plugins
  );

  // Use null references when using native scrollbar
  const effectiveEmblaRef = useNativeScrollbar ? null : emblaRef;
  const effectiveEmblaApi = useNativeScrollbar ? null : emblaApi;

  // Handle seeking to a specific position (0-1)
  const handleSeek = React.useCallback(
    (position: number) => {
      if (useNativeScrollbar) {
        // For native scrollbar, scroll to position
        if (!containerRef.current) return;
        const contentEl = containerRef.current.querySelector(
          ".carousel-content"
        ) as HTMLElement;
        if (!contentEl) return;

        const maxScrollLeft = contentEl.scrollWidth - contentEl.clientWidth;
        const targetScrollLeft = position * maxScrollLeft;
        contentEl.scrollTo({ left: targetScrollLeft, behavior: "smooth" });
      } else {
        // For Embla carousel, scroll to position
        if (!effectiveEmblaApi) return;
        // Use scrollTo with percentage-based positioning
        const slides = effectiveEmblaApi.slideNodes();
        if (slides.length > 0) {
          const targetSlideIndex = Math.round(position * (slides.length - 1));
          effectiveEmblaApi.scrollTo(targetSlideIndex);
        }
      }
    },
    [useNativeScrollbar, effectiveEmblaApi]
  );

  // Direct scroll handler for immediate response
  const onScroll = React.useCallback(() => {
    if (useNativeScrollbar) {
      // For native scrollbar, track progress manually
      if (!containerRef.current) return;
      const contentEl = containerRef.current.querySelector(
        ".carousel-content"
      ) as HTMLElement;
      if (!contentEl) return;

      const scrollLeft = contentEl.scrollLeft;
      const maxScrollLeft = contentEl.scrollWidth - contentEl.clientWidth;
      const progress = maxScrollLeft > 0 ? scrollLeft / maxScrollLeft : 0;
      setScrollProgress(Math.max(0, Math.min(1, progress)));

      // Calculate content ratio for native scrollbar
      const visibleWidth = contentEl.clientWidth;
      const totalWidth = contentEl.scrollWidth;
      const ratio = totalWidth > 0 ? visibleWidth / totalWidth : 1;
      setContentRatio(Math.max(0.1, Math.min(0.9, ratio)));
      return;
    }

    if (!effectiveEmblaApi || !shouldScroll) return;
    const progress = Math.max(
      0,
      Math.min(1, effectiveEmblaApi.scrollProgress())
    );
    setScrollProgress(progress);

    // Calculate content ratio for Embla carousel
    const slides = effectiveEmblaApi.slideNodes();
    if (slides.length > 0) {
      // For Embla, we can estimate based on slide count and container
      const containerWidth = containerRef.current?.clientWidth || 0;
      const slideWidth = slides[0]?.offsetWidth || 0;
      const visibleSlides =
        containerWidth > 0 && slideWidth > 0 ? containerWidth / slideWidth : 1;
      const ratio =
        slides.length > 0 ? Math.min(1, visibleSlides / slides.length) : 1;
      setContentRatio(Math.max(0.1, Math.min(0.9, ratio)));
    }
  }, [effectiveEmblaApi, shouldScroll, useNativeScrollbar]);

  // Function to check if scrolling is needed
  const checkScrollNeeded = React.useCallback(() => {
    if (!containerRef.current) return;

    if (useNativeScrollbar) {
      // For native scrollbar, we always want to show the scrollbar if content overflows
      const containerEl = containerRef.current;
      const contentEl = containerEl.querySelector(".carousel-content");

      if (contentEl) {
        // Check if content width exceeds container width
        const needsScroll = contentEl.scrollWidth > containerEl.clientWidth;
        setShouldScroll(needsScroll);
      }
      return;
    }

    if (!effectiveEmblaApi) return;

    // Get the container width
    const containerWidth = containerRef.current.clientWidth;

    // Get the total content width (sum of all slides)
    const slides = effectiveEmblaApi.slideNodes();

    // Use a standard gap size for calculation (18px for gap-4.5)
    // This is an approximation as we can't easily extract the exact gap size from the className
    const gapSizeInPx = 18; // Default gap size in pixels

    const totalContentWidth =
      slides.reduce((total, slide) => {
        const slideWidth = slide.offsetWidth;
        return total + slideWidth;
      }, 0) +
      (slides.length - 1) * gapSizeInPx; // Add gaps between slides

    // Determine if scrolling is needed
    const needsScroll = totalContentWidth > containerWidth;

    // Only update state if it's different to avoid unnecessary re-renders
    if (shouldScroll !== needsScroll) {
      setShouldScroll(needsScroll);

      // If content fits, reset scroll position to start
      if (!needsScroll && effectiveEmblaApi) {
        effectiveEmblaApi.scrollTo(0);
        setScrollProgress(0);
      }
    }
  }, [effectiveEmblaApi, shouldScroll, useNativeScrollbar]);

  React.useEffect(() => {
    // Set up resize observer to check if scrolling is needed when window size changes
    const resizeObserver = new ResizeObserver(() => {
      checkScrollNeeded();
    });

    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);

      // Initial check if scrolling is needed
      checkScrollNeeded();
    }

    // For native scrollbar, set up scroll event listener
    if (useNativeScrollbar) {
      const contentEl = containerRef.current?.querySelector(
        ".carousel-content"
      ) as HTMLElement;
      if (contentEl) {
        contentEl.addEventListener("scroll", onScroll);
        // Initial progress calculation
        onScroll();
      }

      return () => {
        if (contentEl) {
          contentEl.removeEventListener("scroll", onScroll);
        }
        resizeObserver.disconnect();
      };
    }

    // For Embla carousel
    if (effectiveEmblaApi) {
      // Set up scroll event listener
      effectiveEmblaApi.on("scroll", onScroll);

      // Initial scroll progress
      onScroll();

      // Clean up
      return () => {
        effectiveEmblaApi.off("scroll", onScroll);
        resizeObserver.disconnect();
      };
    }

    // Fallback cleanup
    return () => {
      resizeObserver.disconnect();
    };
  }, [effectiveEmblaApi, onScroll, checkScrollNeeded, useNativeScrollbar]);

  // Convert children to array to work with them
  const childrenArray = React.Children.toArray(children);

  return (
    <div
      className={cn("relative flex flex-col w-full", className)}
      ref={containerRef}
      data-scroll-progress={Math.round(scrollProgress * 100)}
    >
      {useNativeScrollbar ? (
        // Native scrollbar version
        <>
          <div
            className={cn(
              "w-full carousel-content rounded-md",
              shouldScroll
                ? "overflow-x-auto overflow-y-hidden"
                : "overflow-hidden",
              !shouldScroll && "flex flex-wrap justify-center",
              (hideScrollbar || showProgressBar) && "scrollbar-hide" // Hide native scrollbar when using custom progress bar
            )}
            style={{
              scrollbarWidth:
                hideScrollbar || showProgressBar ? "none" : "thin", // For Firefox
              msOverflowStyle:
                hideScrollbar || showProgressBar ? "none" : "auto", // For IE and Edge
              WebkitOverflowScrolling: "touch", // For iOS momentum scrolling
              // Add custom scrollbar color if provided and not using progress bar
              ...(!showProgressBar && scrollbarColor && !hideScrollbar
                ? {
                    // For WebKit browsers (Chrome, Safari)
                    "--scrollbar-thumb-color": scrollbarColor,
                    // For Firefox
                    "--scrollbar-track-color": scrollBarTrackColor,
                    // For Firefox
                    scrollbarColor: `${scrollbarColor} ${scrollBarTrackColor}`,
                  }
                : !showProgressBar && {
                    // Default track color when scrollbar is visible but no custom color is provided
                    "--scrollbar-track-color": "#FFF7E7",
                    scrollbarColor: `#888 #FFF7E7`,
                  }),
              // Padding is now handled in CSS
            }}
            data-scrollable={
              shouldScroll && !hideScrollbar && !showProgressBar
                ? "true"
                : "false"
            }
            aria-label="Scrollable content"
          >
            <div className={`flex ${gapClassName} min-w-max h-auto`}>
              {childrenArray.map((child, index) => (
                <div
                  key={index}
                  className={cn(
                    "flex-shrink-0 min-w-0 flex justify-center items-center overflow-hidden",
                    itemClassName,
                    !shouldScroll && "flex-grow-0"
                  )}
                >
                  {child}
                </div>
              ))}
            </div>
          </div>

          {/* Native Scrollbar with Interactive Overlay */}
          {shouldScroll && showProgressBar && (
            <div className="relative mt-6">
              {/* Native scrollbar styling */}
              <style jsx>{`
                .native-scrollbar::-webkit-scrollbar {
                  height: 6px;
                  width: 6px;
                }
                .native-scrollbar::-webkit-scrollbar-track {
                  background: ${progressBarTrackColor};
                  border-radius: 3px;
                }
                .native-scrollbar::-webkit-scrollbar-thumb {
                  background: ${progressBarThumbColor};
                  border-radius: 3px;
                }
                .native-scrollbar::-webkit-scrollbar-thumb:hover {
                  background: ${progressBarThumbColor}dd;
                }
                .native-scrollbar {
                  scrollbar-width: thin;
                  scrollbar-color: ${progressBarThumbColor}
                    ${progressBarTrackColor};
                }
              `}</style>

              {/* Interactive overlay */}
              <NativeScrollbarOverlay
                trackColor={progressBarTrackColor}
                thumbColor={progressBarThumbColor}
                showProgressBar={showProgressBar}
                onSeek={handleSeek}
                containerRef={containerRef}
                className="relative"
              />
            </div>
          )}
        </>
      ) : (
        // Embla carousel version
        <>
          <div
            id="carousel-content"
            className={cn(
              "overflow-x-hidden overflow-y-hidden w-full carousel-content rounded-md",
              !shouldScroll && "overflow-visible",
              hideScrollbar && "scrollbar-hide"
            )}
            ref={effectiveEmblaRef}
            data-scrollable={
              shouldScroll && !hideScrollbar && !showProgressBar
                ? "true"
                : "false"
            }
            aria-label={
              shouldScroll
                ? "Scrollable content, swipe or use trackpad to scroll horizontally"
                : "Content"
            }
          >
            <div
              className={cn(
                `flex ${gapClassName} h-auto`,
                !shouldScroll && "flex-wrap justify-center"
              )}
            >
              {childrenArray.map((child, index) => (
                <div
                  key={index}
                  className={cn(
                    "flex-shrink-0 min-w-0 flex justify-center items-center overflow-hidden",
                    itemClassName,
                    // If no scrolling needed, allow items to wrap
                    !shouldScroll && "flex-grow-0"
                  )}
                >
                  {child}
                </div>
              ))}
            </div>
          </div>

          {/* Native Scrollbar with Interactive Overlay for Embla */}
          {shouldScroll && showProgressBar && (
            <div className="relative mt-6">
              {/* Native scrollbar styling */}
              <style jsx>{`
                .native-scrollbar::-webkit-scrollbar {
                  height: 6px;
                  width: 6px;
                }
                .native-scrollbar::-webkit-scrollbar-track {
                  background: ${progressBarTrackColor};
                  border-radius: 3px;
                }
                .native-scrollbar::-webkit-scrollbar-thumb {
                  background: ${progressBarThumbColor};
                  border-radius: 3px;
                }
                .native-scrollbar::-webkit-scrollbar-thumb:hover {
                  background: ${progressBarThumbColor}dd;
                }
                .native-scrollbar {
                  scrollbar-width: thin;
                  scrollbar-color: ${progressBarThumbColor}
                    ${progressBarTrackColor};
                }
              `}</style>

              {/* Interactive overlay */}
              <NativeScrollbarOverlay
                trackColor={progressBarTrackColor}
                thumbColor={progressBarThumbColor}
                showProgressBar={showProgressBar}
                onSeek={handleSeek}
                containerRef={containerRef}
                className="relative"
              />
            </div>
          )}
        </>
      )}
    </div>
  );
}
