"use client";

import * as React from "react";
import useEmblaCarousel from "embla-carousel-react";
import { WheelGesturesPlugin } from "embla-carousel-wheel-gestures";
import type { EmblaOptionsType } from "embla-carousel";
import { cn } from "@/libs/utils";

// Always-visible custom scrollbar with native behavior

interface AlwaysVisibleScrollbarProps {
  progress: number;
  contentRatio: number;
  trackColor?: string;
  thumbColor?: string;
  showProgressBar?: boolean;
  className?: string;
  onSeek?: (position: number) => void;
}

// Always-visible custom scrollbar with native behavior
const AlwaysVisibleScrollbar: React.FC<AlwaysVisibleScrollbarProps> =
  React.memo(
    ({
      progress,
      contentRatio,
      trackColor = "#FFF7E7",
      thumbColor = "#888",
      showProgressBar = true,
      className = "",
      onSeek,
    }) => {
      const trackRef = React.useRef<HTMLDivElement>(null);
      const thumbRef = React.useRef<HTMLDivElement>(null);
      const [isDragging, setIsDragging] = React.useState(false);
      const [isHoveringThumb, setIsHoveringThumb] = React.useState(false);

      // Calculate thumb width as percentage of track (minimum 10%, maximum 90%)
      const thumbWidthPercent = Math.max(10, Math.min(90, contentRatio * 100));

      // Calculate available space for thumb movement
      const availableSpace = 100 - thumbWidthPercent;

      // Calculate thumb position based on scroll progress
      const thumbPosition = Math.max(
        0,
        Math.min(availableSpace, progress * availableSpace)
      );

      // Convert pixel position to progress value (0-1)
      const getProgressFromPosition = React.useCallback(
        (clientX: number) => {
          if (!trackRef.current) return 0;

          const rect = trackRef.current.getBoundingClientRect();
          const relativeX = clientX - rect.left;
          const trackWidth = rect.width;

          // Calculate position as percentage of track
          const positionPercent = Math.max(
            0,
            Math.min(100, (relativeX / trackWidth) * 100)
          );

          // Convert to progress value considering thumb width
          const maxPosition = 100 - thumbWidthPercent;
          const adjustedPosition = Math.max(
            0,
            Math.min(maxPosition, positionPercent - thumbWidthPercent / 2)
          );

          return maxPosition > 0 ? adjustedPosition / maxPosition : 0;
        },
        [thumbWidthPercent]
      );

      // Handle click on track (click-to-seek)
      const handleTrackClick = React.useCallback(
        (event: React.MouseEvent) => {
          if (!onSeek || isDragging) return;
          const newProgress = getProgressFromPosition(event.clientX);
          onSeek(newProgress);
        },
        [onSeek, isDragging, getProgressFromPosition]
      );

      // Handle mouse down on thumb (start drag)
      const handleThumbMouseDown = React.useCallback(
        (event: React.MouseEvent) => {
          event.preventDefault();
          event.stopPropagation();
          setIsDragging(true);
        },
        []
      );

      // Handle mouse move during drag with immediate response
      const handleMouseMove = React.useCallback(
        (event: MouseEvent) => {
          if (!isDragging || !onSeek) return;
          event.preventDefault();
          const newProgress = getProgressFromPosition(event.clientX);
          onSeek(newProgress);
        },
        [isDragging, onSeek, getProgressFromPosition]
      );

      // Handle mouse up (end drag)
      const handleMouseUp = React.useCallback(() => {
        setIsDragging(false);
      }, []);

      // Handle touch events for mobile
      const handleTouchStart = React.useCallback((event: React.TouchEvent) => {
        event.preventDefault();
        setIsDragging(true);
      }, []);

      const handleTouchMove = React.useCallback(
        (event: TouchEvent) => {
          if (!isDragging || !onSeek) return;
          event.preventDefault();

          const touch = event.touches[0];
          if (touch) {
            const newProgress = getProgressFromPosition(touch.clientX);
            onSeek(newProgress);
          }
        },
        [isDragging, onSeek, getProgressFromPosition]
      );

      const handleTouchEnd = React.useCallback(() => {
        setIsDragging(false);
      }, []);

      // Set up global event listeners for dragging
      React.useEffect(() => {
        if (isDragging) {
          document.addEventListener("mousemove", handleMouseMove);
          document.addEventListener("mouseup", handleMouseUp);
          document.addEventListener("touchmove", handleTouchMove);
          document.addEventListener("touchend", handleTouchEnd);

          return () => {
            document.removeEventListener("mousemove", handleMouseMove);
            document.removeEventListener("mouseup", handleMouseUp);
            document.removeEventListener("touchmove", handleTouchMove);
            document.removeEventListener("touchend", handleTouchEnd);
          };
        }
      }, [
        isDragging,
        handleMouseMove,
        handleMouseUp,
        handleTouchMove,
        handleTouchEnd,
      ]);

      if (!showProgressBar) return null;

      return (
        <div className={`w-full ${className}`}>
          <div
            ref={trackRef}
            className="w-full h-1.5 rounded-sm relative overflow-hidden select-none"
            style={{
              backgroundColor: trackColor,
              cursor: onSeek ? "pointer" : "default",
            }}
            onClick={handleTrackClick}
          >
            <div
              ref={thumbRef}
              className={cn(
                "h-1.5 rounded-sm absolute top-0 select-none transition-none",
                isDragging
                  ? "transition-none"
                  : "transition-transform duration-150 ease-out"
              )}
              style={{
                backgroundColor: thumbColor,
                width: `${thumbWidthPercent}%`,
                left: `${thumbPosition}%`,
                cursor: onSeek
                  ? isDragging
                    ? "grabbing"
                    : isHoveringThumb
                    ? "grab"
                    : "pointer"
                  : "default",
              }}
              onMouseDown={handleThumbMouseDown}
              onMouseEnter={() => setIsHoveringThumb(true)}
              onMouseLeave={() => setIsHoveringThumb(false)}
              onTouchStart={handleTouchStart}
            />
          </div>
        </div>
      );
    }
  );

AlwaysVisibleScrollbar.displayName = "AlwaysVisibleScrollbar";

interface UniversalCarouselProps {
  children: React.ReactNode;
  className?: string;
  itemClassName?: string;
  options?: EmblaOptionsType;
  useNativeScrollbar?: boolean;
  hideScrollbar?: boolean;
  gapClassName?: string;
  scrollbarColor?: string;
  scrollBarTrackColor?: string;
  showProgressBar?: boolean;
  progressBarTrackColor?: string;
  progressBarThumbColor?: string;
}

export function UniversalCarousel({
  children,
  className,
  itemClassName,
  options = {
    align: "start",
    skipSnaps: false,
    dragFree: true,
  },
  useNativeScrollbar = false,
  hideScrollbar = false,
  gapClassName = "gap-5",
  scrollbarColor,
  scrollBarTrackColor,
  showProgressBar = true,
  progressBarTrackColor = "#FFF7E7",
  progressBarThumbColor = "#888",
}: UniversalCarouselProps) {
  const containerRef = React.useRef<HTMLDivElement>(null);
  const [shouldScroll, setShouldScroll] = React.useState(true);
  const [scrollProgress, setScrollProgress] = React.useState(0);
  const [contentRatio, setContentRatio] = React.useState(0.3); // Ratio of visible to total content

  // Configure the wheel gestures plugin
  const wheelGesturesPlugin = WheelGesturesPlugin({
    forceWheelAxis: "x",
    wheelEnabled: true,
    wheelToScroll: true,
  });

  // Only include the wheel plugin if scrolling is needed and not using native scrollbar
  const plugins =
    shouldScroll && !useNativeScrollbar ? [wheelGesturesPlugin] : [];

  // Always call useEmblaCarousel hook unconditionally
  const [emblaRef, emblaApi] = useEmblaCarousel(
    {
      ...options,
      // Enable/disable dragging based on whether content needs scrolling
      watchDrag: shouldScroll,
    },
    plugins
  );

  // Use null references when using native scrollbar
  const effectiveEmblaRef = useNativeScrollbar ? null : emblaRef;
  const effectiveEmblaApi = useNativeScrollbar ? null : emblaApi;

  // Handle seeking to a specific position (0-1)
  const handleSeek = React.useCallback(
    (position: number) => {
      if (useNativeScrollbar) {
        // For native scrollbar, scroll to position
        if (!containerRef.current) return;
        const contentEl = containerRef.current.querySelector(
          ".carousel-content"
        ) as HTMLElement;
        if (!contentEl) return;

        const maxScrollLeft = contentEl.scrollWidth - contentEl.clientWidth;
        const targetScrollLeft = position * maxScrollLeft;
        contentEl.scrollTo({ left: targetScrollLeft, behavior: "smooth" });
      } else {
        // For Embla carousel, scroll to position
        if (!effectiveEmblaApi) return;
        // Use scrollTo with percentage-based positioning
        const slides = effectiveEmblaApi.slideNodes();
        if (slides.length > 0) {
          const targetSlideIndex = Math.round(position * (slides.length - 1));
          effectiveEmblaApi.scrollTo(targetSlideIndex);
        }
      }
    },
    [useNativeScrollbar, effectiveEmblaApi]
  );

  // Direct scroll handler for immediate response
  const onScroll = React.useCallback(() => {
    if (useNativeScrollbar) {
      // For native scrollbar, track progress manually
      if (!containerRef.current) return;
      const contentEl = containerRef.current.querySelector(
        ".carousel-content"
      ) as HTMLElement;
      if (!contentEl) return;

      const scrollLeft = contentEl.scrollLeft;
      const maxScrollLeft = contentEl.scrollWidth - contentEl.clientWidth;
      const progress = maxScrollLeft > 0 ? scrollLeft / maxScrollLeft : 0;
      setScrollProgress(Math.max(0, Math.min(1, progress)));

      // Calculate content ratio for native scrollbar
      const visibleWidth = contentEl.clientWidth;
      const totalWidth = contentEl.scrollWidth;
      const ratio = totalWidth > 0 ? visibleWidth / totalWidth : 1;
      setContentRatio(Math.max(0.1, Math.min(0.9, ratio)));
      return;
    }

    if (!effectiveEmblaApi || !shouldScroll) return;
    const progress = Math.max(
      0,
      Math.min(1, effectiveEmblaApi.scrollProgress())
    );
    setScrollProgress(progress);

    // Calculate content ratio for Embla carousel
    const slides = effectiveEmblaApi.slideNodes();
    if (slides.length > 0) {
      // For Embla, we can estimate based on slide count and container
      const containerWidth = containerRef.current?.clientWidth || 0;
      const slideWidth = slides[0]?.offsetWidth || 0;
      const visibleSlides =
        containerWidth > 0 && slideWidth > 0 ? containerWidth / slideWidth : 1;
      const ratio =
        slides.length > 0 ? Math.min(1, visibleSlides / slides.length) : 1;
      setContentRatio(Math.max(0.1, Math.min(0.9, ratio)));
    }
  }, [effectiveEmblaApi, shouldScroll, useNativeScrollbar]);

  // Function to check if scrolling is needed
  const checkScrollNeeded = React.useCallback(() => {
    if (!containerRef.current) return;

    if (useNativeScrollbar) {
      // For native scrollbar, we always want to show the scrollbar if content overflows
      const containerEl = containerRef.current;
      const contentEl = containerEl.querySelector(".carousel-content");

      if (contentEl) {
        // Check if content width exceeds container width
        const needsScroll = contentEl.scrollWidth > containerEl.clientWidth;
        setShouldScroll(needsScroll);
      }
      return;
    }

    if (!effectiveEmblaApi) return;

    // Get the container width
    const containerWidth = containerRef.current.clientWidth;

    // Get the total content width (sum of all slides)
    const slides = effectiveEmblaApi.slideNodes();

    // Use a standard gap size for calculation (18px for gap-4.5)
    // This is an approximation as we can't easily extract the exact gap size from the className
    const gapSizeInPx = 18; // Default gap size in pixels

    const totalContentWidth =
      slides.reduce((total, slide) => {
        const slideWidth = slide.offsetWidth;
        return total + slideWidth;
      }, 0) +
      (slides.length - 1) * gapSizeInPx; // Add gaps between slides

    // Determine if scrolling is needed
    const needsScroll = totalContentWidth > containerWidth;

    // Only update state if it's different to avoid unnecessary re-renders
    if (shouldScroll !== needsScroll) {
      setShouldScroll(needsScroll);

      // If content fits, reset scroll position to start
      if (!needsScroll && effectiveEmblaApi) {
        effectiveEmblaApi.scrollTo(0);
        setScrollProgress(0);
      }
    }
  }, [effectiveEmblaApi, shouldScroll, useNativeScrollbar]);

  React.useEffect(() => {
    // Set up resize observer to check if scrolling is needed when window size changes
    const resizeObserver = new ResizeObserver(() => {
      checkScrollNeeded();
    });

    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);

      // Initial check if scrolling is needed
      checkScrollNeeded();
    }

    // For native scrollbar, set up scroll event listener
    if (useNativeScrollbar) {
      const contentEl = containerRef.current?.querySelector(
        ".carousel-content"
      ) as HTMLElement;
      if (contentEl) {
        contentEl.addEventListener("scroll", onScroll);
        // Initial progress calculation
        onScroll();
      }

      return () => {
        if (contentEl) {
          contentEl.removeEventListener("scroll", onScroll);
        }
        resizeObserver.disconnect();
      };
    }

    // For Embla carousel
    if (effectiveEmblaApi) {
      // Set up scroll event listener
      effectiveEmblaApi.on("scroll", onScroll);

      // Initial scroll progress
      onScroll();

      // Clean up
      return () => {
        effectiveEmblaApi.off("scroll", onScroll);
        resizeObserver.disconnect();
      };
    }

    // Fallback cleanup
    return () => {
      resizeObserver.disconnect();
    };
  }, [effectiveEmblaApi, onScroll, checkScrollNeeded, useNativeScrollbar]);

  // Convert children to array to work with them
  const childrenArray = React.Children.toArray(children);

  return (
    <div
      className={cn("relative flex flex-col w-full", className)}
      ref={containerRef}
      data-scroll-progress={Math.round(scrollProgress * 100)}
    >
      {useNativeScrollbar ? (
        // Native scrollbar version
        <>
          <div
            className={cn(
              "w-full carousel-content rounded-md native-scrollbar",
              shouldScroll
                ? "overflow-x-auto overflow-y-hidden"
                : "overflow-hidden",
              !shouldScroll && "flex flex-wrap justify-center",
              hideScrollbar && "scrollbar-hide" // Hide native scrollbar only when explicitly requested
            )}
            style={{
              scrollbarWidth: hideScrollbar ? "none" : "thin", // For Firefox
              msOverflowStyle: hideScrollbar ? "none" : "auto", // For IE and Edge
              WebkitOverflowScrolling: "touch", // For iOS momentum scrolling
            }}
            data-scrollable={
              shouldScroll && !hideScrollbar && !showProgressBar
                ? "true"
                : "false"
            }
            aria-label="Scrollable content"
          >
            <div className={`flex ${gapClassName} min-w-max h-auto`}>
              {childrenArray.map((child, index) => (
                <div
                  key={index}
                  className={cn(
                    "flex-shrink-0 min-w-0 flex justify-center items-center overflow-hidden",
                    itemClassName,
                    !shouldScroll && "flex-grow-0"
                  )}
                >
                  {child}
                </div>
              ))}
            </div>
          </div>

          {/* Always-Visible Custom Scrollbar */}
          {shouldScroll && (
            <AlwaysVisibleScrollbar
              progress={scrollProgress}
              contentRatio={contentRatio}
              trackColor={progressBarTrackColor}
              thumbColor={progressBarThumbColor}
              showProgressBar={showProgressBar}
              onSeek={handleSeek}
              className={showProgressBar ? "mt-6" : ""}
            />
          )}
        </>
      ) : (
        // Embla carousel version
        <>
          <div
            id="carousel-content"
            className={cn(
              "overflow-x-hidden overflow-y-hidden w-full carousel-content rounded-md",
              !shouldScroll && "overflow-visible",
              hideScrollbar && "scrollbar-hide"
            )}
            ref={effectiveEmblaRef}
            data-scrollable={
              shouldScroll && !hideScrollbar && !showProgressBar
                ? "true"
                : "false"
            }
            aria-label={
              shouldScroll
                ? "Scrollable content, swipe or use trackpad to scroll horizontally"
                : "Content"
            }
          >
            <div
              className={cn(
                `flex ${gapClassName} h-auto`,
                !shouldScroll && "flex-wrap justify-center"
              )}
            >
              {childrenArray.map((child, index) => (
                <div
                  key={index}
                  className={cn(
                    "flex-shrink-0 min-w-0 flex justify-center items-center overflow-hidden",
                    itemClassName,
                    // If no scrolling needed, allow items to wrap
                    !shouldScroll && "flex-grow-0"
                  )}
                >
                  {child}
                </div>
              ))}
            </div>
          </div>

          {/* Always-Visible Custom Scrollbar for Embla */}
          {shouldScroll && (
            <AlwaysVisibleScrollbar
              progress={scrollProgress}
              contentRatio={contentRatio}
              trackColor={progressBarTrackColor}
              thumbColor={progressBarThumbColor}
              showProgressBar={showProgressBar}
              onSeek={handleSeek}
              className={showProgressBar ? "mt-6" : ""}
            />
          )}
        </>
      )}
    </div>
  );
}
